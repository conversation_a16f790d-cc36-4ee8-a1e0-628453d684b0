<div class="game-container">
  <!-- Notification Component -->
  <app-notification></app-notification>
  <!-- Professional Trading Interface Layout -->
  <div class="trading-layout">
    <!-- Main Content Grid -->
    <div class="main-grid">

      <!-- Left Sidebar: Market Info -->
      <div class="left-sidebar">
        <!-- PM Coin Info -->
        <div class="market-card" *ngIf="pmCoinData">
          <div class="card-header">
            <div class="coin-info">
              <div class="coin-icon">PM</div>
              <div class="coin-details">
                <h3>PM Coin</h3>
                <span class="symbol">PMC/USD</span>
              </div>
            </div>
            <div class="live-badge">
              <span class="live-dot"></span>
              LIVE
            </div>
          </div>

          <div class="price-section">
            <div class="current-price" [class]="getPriceChangeClass()">
              ${{ formatPrice(pmCoinData.currentPrice) }}
            </div>
            <div class="price-change" [class]="getChange24hClass()">
              <span class="change-icon">{{ pmCoinData.change24h >= 0 ? '↗' : '↘' }}</span>
              {{ formatPercentage(pmCoinData.change24h) }}
            </div>
          </div>

          <!-- Live Market Activity Section -->
          <div class="activity-section">
            <div class="activity-header">
              <h4>📊 Hoạt Động Thị Trường</h4>
              <div class="activity-status">
                <div class="activity-dot"></div>
                LIVE
              </div>
            </div>

            <div class="activity-feed">
              <div *ngFor="let transaction of recentTransactions | slice:0:5; trackBy: trackTransaction"
                class="activity-item" [class]="transaction.action">
                <div class="transaction-icon">
                  {{ transaction.action === 'buy' ? '🟢' : '🔴' }}
                </div>
                <div class="transaction-details">
                  <div class="transaction-wallet">{{ formatWalletAddress(transaction.walletAddress) }}</div>
                  <div class="transaction-info">
                    {{ transaction.action === 'buy' ? 'mua' : 'bán' }}
                    {{ formatAmount(transaction.amount) }} PMC
                  </div>
                </div>
                <div class="transaction-price">
                  ${{ formatPrice(transaction.price) }}
                </div>
              </div>

              <!-- Empty state khi chưa có data -->
              <div *ngIf="recentTransactions.length === 0" class="activity-empty">
                <div class="loading-dots">
                  <span></span><span></span><span></span>
                </div>
                <p>Đang tải hoạt động thị trường...</p>
              </div>
            </div>
          </div>
        </div>

      </div>

      <!-- Center: Professional Chart -->
      <div class="chart-area">
        <div class="chart-card">
          <div class="chart-header">
            <h2>Biểu đồ PM Coin</h2>
            <div class="chart-controls">
              <span class="data-points" *ngIf="pmCoinData">
                {{ pmCoinData.priceHistory.length }} điểm dữ liệu
              </span>
            </div>
          </div>

          <div class="chart-viewport">
            <!-- TradingView Chart Container -->
            <div class="chart-container-wrapper">
              <div #priceChart class="tradingview-chart"></div>

              <!-- Chart Loading -->
              <div class="chart-loading" *ngIf="!pmCoinData">
                <div class="loading-animation">
                  <div class="loading-pulse"></div>
                  <div class="loading-bars">
                    <div class="bar" *ngFor="let bar of [1,2,3,4,5,6,7,8]" [style.animation-delay]="bar * 0.1 + 's'">
                    </div>
                  </div>
                </div>
                <h3>Tải dữ liệu ....</h3>
                <p>Kết nối với nguồn cấp giá PM Coin</p>
              </div>

              <!-- Price Info Overlay -->
              <div class="price-info-overlay" *ngIf="pmCoinData">
                <div class="current-price-display">
                  <div class="price-main" [class]="getPriceChangeClass()">
                    ${{ formatPrice(pmCoinData.currentPrice) }}
                  </div>
                  <div class="price-change" [class]="getChange24hClass()">
                    <span class="change-arrow">{{ pmCoinData.change24h >= 0 ? '↗' : '↘' }}</span>
                    {{ formatPercentage(pmCoinData.change24h) }}
                    <span class="change-amount">({{ pmCoinData.change24h >= 0 ? '+' : '' }}${{
                      formatPrice(pmCoinData.change24h >= 0 ?
                      (pmCoinData.change24h * pmCoinData.currentPrice / 100) :
                      -(pmCoinData.change24h * pmCoinData.currentPrice / 100)) }})</span>
                  </div>
                </div>

                <!-- Chart Stats -->
                <div class="chart-stats">
                  <div class="stat-item">
                    <span class="stat-label">24h High</span>
                    <span class="stat-value">${{ formatPrice(pmCoinData.currentPrice * 1.05) }}</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">24h Low</span>
                    <span class="stat-value">${{ formatPrice(pmCoinData.currentPrice * 0.95) }}</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">Volume</span>
                    <span class="stat-value">{{ formatNumber(pmCoinData.volume || 0) }}</span>
                  </div>
                </div>
              </div>

              <!-- Chart Crosshair Info -->
              <div class="crosshair-info" id="crosshairInfo" style="display: none;">
                <div class="crosshair-time"></div>
                <div class="crosshair-price"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Panel: Game Interface -->
      <div class="right-panel">
        <!-- Betting Interface -->
        <div class="betting-card">
          <div class="card-header">
            <h3> Nơi để Đặt Cược</h3>
            <div class="countdown-timer">
              <div class="timer-circle"
                [class.warning]="gameState.timeLeft <= 10 && gameState.timeLeft > 0"
                [class.critical]="gameState.timeLeft <= 5 && gameState.timeLeft > 0"
                [class.finished]="gameState.timeLeft <= 0">
                <span class="timer-value">{{ gameState.timeLeft <= 0 ? '0' : gameState.timeLeft }}</span>
                <span class="timer-label">s</span>
              </div>
              <div class="timer-text">
                {{ gameState.timeLeft <= 0 ? 'Đang xử lý kết quả...' : 'Thời gian đặt cược' }}
              </div>
            </div>
          </div>

          <!-- Bet Amount Input -->
          <div class="bet-amount-section">
            <label class="section-label">Mức cược</label>
            <div class="amount-input-wrapper">
              <span class="currency-symbol">$</span>
              <input type="number" [(ngModel)]="betAmount" class="amount-input" placeholder="0.00" min="1"
                [max]="balance">
            </div>

            <!-- Quick Amount Buttons -->
            <div class="quick-amounts">
              <button *ngFor="let amount of quickAmounts" class="quick-btn" (click)="setBetAmount(amount)"
                [class.active]="betAmount === amount">
                ${{ amount }}
              </button>
            </div>
          </div>

          <!-- Auto-Deposit Info -->
          <div class="auto-deposit-info" *ngIf="bettingService.userBalance$ | async as balances">
            <div class="info-text" *ngIf="getTotalBalance(balances) >= betAmount && getGameBalance(balances) < betAmount">
              <small>💡 Sẽ tự động approve + nạp {{ formatPMAmount(getDepositAmount(balances)) }} PM vào game</small>
            </div>
          </div>

          <!-- Bet Buttons -->
          <div class="bet-buttons-section">
            <button class="bet-btn up-btn" [disabled]="!canPlaceBet()" (click)="placeBet('up')">
              <div class="btn-content">
                <span class="btn-icon">📈</span>
                <span class="btn-text">Up</span>
                <span class="btn-multiplier">×1.9</span>
              </div>
              <div class="btn-glow"></div>
            </button>

            <button class="bet-btn down-btn" [disabled]="!canPlaceBet()" (click)="placeBet('down')">
              <div class="btn-content">
                <span class="btn-icon">📉</span>
                <span class="btn-text">Down</span>
                <span class="btn-multiplier">×1.9</span>
              </div>
              <div class="btn-glow"></div>
            </button>
          </div>

          <!-- Current Bet Display -->
          <div class="current-bet-display" *ngIf="currentBet">
            <div class="bet-header">
              <span class="bet-status">Thông số cược</span>
              <span class="bet-direction" [class]="currentBet.direction">
                {{ currentBet.direction.toUpperCase() }}
              </span>
            </div>
            <div class="bet-details">
              <div class="bet-amount">Mức cược: ${{ formatPrice(currentBet.amount) }}</div>
              <div class="potential-win">Phần thưởng thắng cược: ${{ formatPrice(currentBet.amount * 1.9) }}</div>
              <div class="bet-price">Giá vào: ${{ currentBet.startPrice | number:'1.6-6' }}</div>
              <div class="bet-current-price" *ngIf="pmCoinData?.currentPrice">
                Giá hiện tại: ${{ pmCoinData?.currentPrice | number:'1.6-6' }}
              </div>
            </div>
          </div>

          <!-- Bet Marker for Reference -->
          <div class="bet-marker" *ngIf="bettingService.currentBetMarker$ | async as betMarker">
            <div class="marker-header">
              <i class="fas fa-bookmark"></i>
              <span>📍 Đánh dấu cược - Để đối chiếu kết quả</span>
            </div>
            <div class="marker-details">
              <div class="marker-row">
                <span class="label">🎯 Hướng:</span>
                <span class="value" [class]="'direction-' + betMarker.direction">
                  <i class="fas" [class.fa-arrow-up]="betMarker.direction === 'up'"
                     [class.fa-arrow-down]="betMarker.direction === 'down'"></i>
                  {{ betMarker.direction === 'up' ? 'TĂNG' : 'GIẢM' }}
                </span>
              </div>
              <div class="marker-row">
                <span class="label">💰 Số tiền:</span>
                <span class="value">{{ formatPMAmount(betMarker.amount) }} PM</span>
              </div>
              <div class="marker-row">
                <span class="label">📊 Giá vào:</span>
                <span class="value entry-price">${{ betMarker.startPrice | number:'1.6-6' }}</span>
              </div>
              <div class="marker-row" *ngIf="pmCoinData?.currentPrice">
                <span class="label">📈 Giá hiện tại:</span>
                <span class="value current-price">${{ pmCoinData?.currentPrice | number:'1.6-6' }}</span>
              </div>
              <div class="marker-row" *ngIf="betMarker.startTime">
                <span class="label">⏰ Thời gian:</span>
                <span class="value">{{ formatBetTime(betMarker.startTime) }}</span>
              </div>
              <div class="marker-row" *ngIf="betMarker.result === 'PENDING' && pmCoinData?.currentPrice && betMarker.startPrice > 0">
                <span class="label">🔮 Dự đoán:</span>
                <span class="value prediction-status" [class]="getPredictionStatus(betMarker)">
                  {{ getPredictionStatusText(betMarker) }}
                </span>
              </div>
              <div class="marker-row" *ngIf="betMarker.transactionHash">
                <span class="label">🔗 TX Hash:</span>
                <span class="value tx-hash">{{ formatTxHash(betMarker.transactionHash) }}</span>
              </div>
            </div>
          </div>
        </div>


        <!-- Auto Trading Toggle -->
        <div class="auto-trading-card" *ngIf="false">
          <div class="card-header">
            <h3>🤖 Giao Dịch Tự Động</h3>
            <div class="toggle-switch">
              <input type="checkbox" id="autoTrading" [(ngModel)]="autoTradingEnabled">
              <label for="autoTrading" class="toggle-label"></label>
            </div>
          </div>

          <div class="auto-trading-info" *ngIf="autoTradingEnabled">
            <div class="next-round">
              <span class="label">Next Round:</span>
              <span class="countdown">{{ gameState.timeLeft }}s</span>
            </div>
            <div class="auto-settings">
              <button class="settings-btn">Configure</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>