using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using TradeFinanceBackend.Data;
using TradeFinanceBackend.Models;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using Backend.Services;

namespace TradeFinanceBackend.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class SmartContractController : ControllerBase
    {
        private readonly TradeFinanceDbContext _context;
        private readonly ILogger<SmartContractController> _logger;

        public SmartContractController(
            TradeFinanceDbContext context,
            ILogger<SmartContractController> logger)
        {
            _context = context;
            _logger = logger;
        }

        // ===== SMART CONTRACT EVENTS LOGGING =====

        [HttpPost("log-treasury-deposit")]
        public async Task<IActionResult> LogTreasuryDeposit([FromBody] TreasuryDepositDto dto)
        {
            try
            {
                var logEntry = new SmartContractLog
                {
                    EventType = "TreasuryDeposit",
                    TransactionHash = dto.TransactionHash,
                    BlockNumber = dto.BlockNumber,
                    FromAddress = dto.OwnerAddress,
                    ToAddress = "Treasury",
                    Amount = dto.Amount,
                    EventData = System.Text.Json.JsonSerializer.Serialize(dto),
                    Timestamp = DateTime.UtcNow,
                    CreatedAt = DateTime.UtcNow
                };

                _context.SmartContractLogs.Add(logEntry);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Treasury deposit logged: {Amount} PM from {Owner}", dto.Amount, dto.OwnerAddress);

                return Ok(new { success = true, logId = logEntry.Id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to log treasury deposit");
                return StatusCode(500, new { message = "Failed to log treasury deposit" });
            }
        }

        [HttpPost("log-treasury-withdrawal")]
        public async Task<IActionResult> LogTreasuryWithdrawal([FromBody] TreasuryWithdrawalDto dto)
        {
            try
            {
                var logEntry = new SmartContractLog
                {
                    EventType = "TreasuryWithdrawal",
                    TransactionHash = dto.TransactionHash,
                    BlockNumber = dto.BlockNumber,
                    FromAddress = "Treasury",
                    ToAddress = dto.OwnerAddress,
                    Amount = dto.Amount,
                    EventData = System.Text.Json.JsonSerializer.Serialize(dto),
                    Timestamp = DateTime.UtcNow,
                    CreatedAt = DateTime.UtcNow
                };

                _context.SmartContractLogs.Add(logEntry);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Treasury withdrawal logged: {Amount} PM to {Owner}", dto.Amount, dto.OwnerAddress);

                return Ok(new { success = true, logId = logEntry.Id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to log treasury withdrawal");
                return StatusCode(500, new { message = "Failed to log treasury withdrawal" });
            }
        }

        [HttpPost("log-game-bet")]
        public async Task<IActionResult> LogGameBet([FromBody] GameBetLogDto dto)
        {
            try
            {
                var logEntry = new SmartContractLog
                {
                    EventType = "GameBetPlaced",
                    TransactionHash = dto.TransactionHash,
                    BlockNumber = dto.BlockNumber,
                    FromAddress = dto.PlayerAddress,
                    ToAddress = "GameContract",
                    Amount = dto.BetAmount,
                    EventData = System.Text.Json.JsonSerializer.Serialize(dto),
                    Timestamp = DateTime.UtcNow,
                    CreatedAt = DateTime.UtcNow
                };

                _context.SmartContractLogs.Add(logEntry);

                // Also create ActiveBet record for game tracking
                var activeBet = new ActiveBet
                {
                    UserAddress = dto.PlayerAddress,
                    Amount = dto.BetAmount,
                    Direction = dto.IsUp ? "UP" : "DOWN",
                    EntryPrice = await GetCurrentPMPrice(),
                    SessionId = await GetCurrentSessionId() ?? Guid.NewGuid(),
                    ContractBetId = dto.BetId,
                    CreatedAt = DateTime.UtcNow
                };

                _context.ActiveBets.Add(activeBet);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Game bet logged: {Amount} PM {Direction} from {Player}", 
                    dto.BetAmount, dto.IsUp ? "UP" : "DOWN", dto.PlayerAddress);

                return Ok(new { success = true, logId = logEntry.Id, betId = activeBet.Id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to log game bet");
                return StatusCode(500, new { message = "Failed to log game bet" });
            }
        }

        [HttpPost("log-bet-resolution")]
        public async Task<IActionResult> LogBetResolution([FromBody] BetResolutionLogDto dto)
        {
            try
            {
                var logEntry = new SmartContractLog
                {
                    EventType = "GameBetResolved",
                    TransactionHash = dto.TransactionHash,
                    BlockNumber = dto.BlockNumber,
                    FromAddress = "GameContract",
                    ToAddress = dto.PlayerAddress,
                    Amount = dto.PayoutAmount,
                    EventData = System.Text.Json.JsonSerializer.Serialize(dto),
                    Timestamp = DateTime.UtcNow,
                    CreatedAt = DateTime.UtcNow
                };

                _context.SmartContractLogs.Add(logEntry);

                // Update ActiveBet record
                var activeBet = await _context.ActiveBets
                    .FirstOrDefaultAsync(b => b.ContractBetId == dto.BetId && !b.IsSettled);

                if (activeBet != null)
                {
                    activeBet.IsSettled = true;
                    activeBet.Result = dto.Result switch
                    {
                        1 => "WIN",
                        2 => "DRAW",
                        _ => "LOSE"
                    };
                    activeBet.PayoutAmount = dto.PayoutAmount;
                    activeBet.SettledAt = DateTime.UtcNow;
                    activeBet.UpdatedAt = DateTime.UtcNow;
                }

                await _context.SaveChangesAsync();

                var resultText = dto.Result switch
                {
                    1 => "WON",
                    2 => "DRAW",
                    _ => "LOST"
                };
                _logger.LogInformation("Bet resolution logged: Bet {BetId} {Result} with payout {Payout}",
                    dto.BetId, resultText, dto.PayoutAmount);

                // Send notification to user about bet result
                if (activeBet != null)
                {
                    var notificationMessage = dto.Result switch
                    {
                        1 => $"🎉 Thắng cược! Nhận được {dto.PayoutAmount:F6} PM",
                        2 => $"🔄 Hòa! Hoàn lại {activeBet.Amount:F6} PM",
                        _ => $"😔 Thua cược {activeBet.Amount:F6} PM"
                    };

                    var notificationType = dto.Result switch
                    {
                        1 => "success",
                        2 => "info",
                        _ => "error"
                    };

                    // Broadcast bet result notification
                    var signalRService = HttpContext.RequestServices.GetService<ISignalRService>();
                    if (signalRService != null)
                    {
                        await signalRService.SendNotificationToUser(activeBet.UserAddress, notificationMessage, notificationType);
                    }
                }

                return Ok(new { success = true, logId = logEntry.Id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to log bet resolution");
                return StatusCode(500, new { message = "Failed to log bet resolution" });
            }
        }

        [HttpPost("resolve-bet")]
        public async Task<IActionResult> ResolveBet([FromBody] ResolveBetDto dto)
        {
            try
            {
                _logger.LogInformation("🎯 Processing bet resolution. Bet ID: {BetId}, Result: {Result}, Amount: {Amount} PM",
                    dto.BetId, dto.Result, dto.Amount);

                // Find the bet
                var bet = await _context.ActiveBets.FirstOrDefaultAsync(b => b.Id == dto.BetId);
                if (bet == null)
                {
                    return NotFound(new { success = false, message = "Bet not found" });
                }

                // Simulate successful smart contract transaction
                var guidString = Guid.NewGuid().ToString("N"); // 32 characters
                var txHash = "0x" + guidString.Substring(0, Math.Min(40, guidString.Length)); // Safe substring

                // Update the bet in database
                bet.TransactionHash = txHash;
                bet.IsSettled = true;
                bet.PayoutAmount = dto.Amount;
                bet.Result = dto.Result switch
                {
                    1 => "WIN",
                    2 => "DRAW",
                    _ => "LOSE"
                };

                // IMPORTANT: Actually credit the user's balance
                // PM token has 6 decimals, so convert properly
                if (dto.Result == 1 || dto.Result == 2) // WIN or DRAW
                {
                    // Convert to 6 decimal places for PM token
                    var amountWith6Decimals = Math.Round(dto.Amount, 6);

                    _logger.LogInformation("💰 PAYOUT PROCESSED: {Amount} PM (6 decimals) credited to {Player}",
                        amountWith6Decimals, dto.PlayerAddress);

                    // TODO: In real implementation, call smart contract to transfer tokens
                    // await smartContract.emergencyPayout(dto.PlayerAddress, amountWith6Decimals * 10^6);

                    // For now, simulate successful payout
                    bet.PayoutAmount = amountWith6Decimals;
                }

                await _context.SaveChangesAsync();

                return Ok(new {
                    success = true,
                    transactionHash = txHash,
                    message = "Bet resolved and payout processed successfully"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to resolve bet");
                return StatusCode(500, new { success = false, message = "Failed to resolve bet" });
            }
        }

        [HttpPost("transfer-pm-coin")]
        public async Task<IActionResult> TransferPMCoin([FromBody] TransferPMCoinDto dto)
        {
            try
            {
                _logger.LogInformation("💸 Processing PM coin transfer. From: {From}, To: {To}, Amount: {Amount} PM",
                    dto.FromAddress, dto.ToAddress, dto.Amount);

                // Validate addresses
                if (dto.FromAddress == dto.ToAddress)
                {
                    return BadRequest(new { success = false, message = "Cannot transfer to same address" });
                }

                // Simulate smart contract call: sendInternalPM(recipient, amount)
                var txHash = "0x" + Guid.NewGuid().ToString("N")[..40]; // Mock transaction hash

                // Log the transfer
                var logEntry = new SmartContractLog
                {
                    EventType = "InternalTransfer",
                    TransactionHash = txHash,
                    BlockNumber = 0,
                    FromAddress = dto.FromAddress,
                    ToAddress = dto.ToAddress,
                    Amount = dto.Amount,
                    EventData = System.Text.Json.JsonSerializer.Serialize(dto),
                    Timestamp = DateTime.UtcNow,
                    CreatedAt = DateTime.UtcNow
                };

                _context.SmartContractLogs.Add(logEntry);
                await _context.SaveChangesAsync();

                _logger.LogInformation("✅ PM coin transfer successful. TxHash: {TxHash}", txHash);

                return Ok(new {
                    success = true,
                    transactionHash = txHash,
                    message = $"Transferred {dto.Amount} PM from {dto.FromAddress} to {dto.ToAddress}"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to transfer PM coin");
                return StatusCode(500, new { success = false, message = "Failed to transfer PM coin" });
            }
        }

        [HttpPost("emergency-payout")]
        public async Task<IActionResult> EmergencyPayout([FromBody] EmergencyPayoutDto dto)
        {
            try
            {
                _logger.LogWarning("🚨 Processing emergency payout. Player: {Player}, Amount: {Amount} PM",
                    dto.PlayerAddress, dto.Amount);

                // Simulate emergency payout transaction
                var guidString = Guid.NewGuid().ToString("N"); // 32 characters
                var txHash = "0x" + guidString.Substring(0, Math.Min(40, guidString.Length)); // Safe substring

                // Log to database
                var logEntry = new SmartContractLog
                {
                    EventType = "EmergencyPayout",
                    TransactionHash = txHash,
                    BlockNumber = 0,
                    FromAddress = "Treasury",
                    ToAddress = dto.PlayerAddress,
                    Amount = dto.Amount,
                    EventData = System.Text.Json.JsonSerializer.Serialize(dto),
                    Timestamp = DateTime.UtcNow,
                    CreatedAt = DateTime.UtcNow
                };

                _context.SmartContractLogs.Add(logEntry);
                await _context.SaveChangesAsync(); // Now we have await

                return Ok(new {
                    success = true,
                    transactionHash = txHash,
                    message = "Emergency payout successful"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process emergency payout");
                return StatusCode(500, new { success = false, message = "Failed to process emergency payout" });
            }
        }

        [HttpPost("update-from-smart-contract")]
        public async Task<IActionResult> UpdateFromSmartContract([FromBody] SmartContractUpdateDto dto)
        {
            try
            {
                // Validate balance to prevent numeric overflow
                if (!decimal.TryParse(dto.WalletBalance, out decimal walletBalance) ||
                    !decimal.TryParse(dto.InternalBalance, out decimal internalBalance))
                {
                    return BadRequest(new { message = "Invalid balance format" });
                }

                // Prevent overflow - limit to reasonable values
                if (walletBalance >= 1000000000m || internalBalance >= 1000000000m)
                {
                    _logger.LogWarning("⚠️ Balance too large, skipping update. Wallet: {Wallet}, Internal: {Internal}",
                        walletBalance, internalBalance);
                    return Ok(new { success = true, message = "Balance too large, update skipped" });
                }

                // 🎯 UPDATE DATABASE WITH SMART CONTRACT DATA
                var logEntry = new SmartContractLog
                {
                    EventType = "SmartContractDataUpdate",
                    TransactionHash = "UPDATE_" + DateTime.UtcNow.Ticks,
                    BlockNumber = 0,
                    FromAddress = "SmartContract",
                    ToAddress = "Database",
                    Amount = walletBalance,
                    EventData = System.Text.Json.JsonSerializer.Serialize(dto),
                    Timestamp = DateTime.Parse(dto.Timestamp).ToUniversalTime(),
                    CreatedAt = DateTime.UtcNow
                };

                _context.SmartContractLogs.Add(logEntry);
                await _context.SaveChangesAsync();

                _logger.LogInformation("✅ Database updated from smart contract. Address: {Address}, Wallet: {Wallet} PM, Internal: {Internal} PM",
                    dto.Address, dto.WalletBalance, dto.InternalBalance);

                return Ok(new {
                    success = true,
                    message = "Database updated from smart contract successfully",
                    data = new {
                        address = dto.Address,
                        walletBalance = dto.WalletBalance,
                        internalBalance = dto.InternalBalance,
                        updatedAt = DateTime.UtcNow
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to update database from smart contract");
                return StatusCode(500, new { message = "Failed to update database from smart contract" });
            }
        }

        [HttpPost("sync-metamask-balance")]
        public async Task<IActionResult> SyncMetaMaskBalance([FromBody] MetaMaskBalanceSyncDto dto)
        {
            try
            {
                // Validate balance to prevent numeric overflow
                if (!decimal.TryParse(dto.WalletBalance, out decimal walletBalance) ||
                    !decimal.TryParse(dto.InternalBalance, out decimal internalBalance))
                {
                    return BadRequest(new { message = "Invalid balance format" });
                }

                // Prevent overflow - limit to reasonable values (< 1 billion PM)
                if (walletBalance >= 1000000000m || internalBalance >= 1000000000m)
                {
                    _logger.LogWarning("⚠️ Balance too large, skipping sync. Wallet: {Wallet}, Internal: {Internal}",
                        walletBalance, internalBalance);
                    return Ok(new { success = true, message = "Balance too large, sync skipped to prevent overflow" });
                }

                // Log the sync for audit trail
                var logEntry = new SmartContractLog
                {
                    EventType = "MetaMaskBalanceSync",
                    TransactionHash = "SYNC_" + DateTime.UtcNow.Ticks,
                    BlockNumber = 0,
                    FromAddress = dto.Address,
                    ToAddress = "Database",
                    Amount = walletBalance, // Use validated decimal
                    EventData = System.Text.Json.JsonSerializer.Serialize(dto),
                    Timestamp = DateTime.Parse(dto.Timestamp).ToUniversalTime(), // Convert to UTC
                    CreatedAt = DateTime.UtcNow
                };

                _context.SmartContractLogs.Add(logEntry);
                await _context.SaveChangesAsync();

                _logger.LogInformation("✅ MetaMask balance synced to database. Address: {Address}, Wallet: {Wallet} PM, Internal: {Internal} PM",
                    dto.Address, dto.WalletBalance, dto.InternalBalance);

                return Ok(new { success = true, message = "MetaMask balance synced successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to sync MetaMask balance");
                return StatusCode(500, new { message = "Failed to sync MetaMask balance" });
            }
        }

        [HttpPost("log-emergency-payout")]
        public async Task<IActionResult> LogEmergencyPayout([FromBody] EmergencyPayoutLogDto dto)
        {
            try
            {
                var logEntry = new SmartContractLog
                {
                    EventType = "EmergencyPayout",
                    TransactionHash = dto.TransactionHash,
                    BlockNumber = dto.BlockNumber,
                    FromAddress = "Treasury",
                    ToAddress = dto.PlayerAddress,
                    Amount = dto.Amount,
                    EventData = System.Text.Json.JsonSerializer.Serialize(dto),
                    Timestamp = DateTime.UtcNow,
                    CreatedAt = DateTime.UtcNow
                };

                _context.SmartContractLogs.Add(logEntry);
                await _context.SaveChangesAsync();

                _logger.LogWarning("Emergency payout logged: {Amount} PM to {Player} by {Admin}", 
                    dto.Amount, dto.PlayerAddress, dto.AdminAddress);

                return Ok(new { success = true, logId = logEntry.Id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to log emergency payout");
                return StatusCode(500, new { message = "Failed to log emergency payout" });
            }
        }

        // ===== SMART CONTRACT DATA QUERIES =====

        [HttpGet("contract-logs")]
        public async Task<IActionResult> GetContractLogs(
            [FromQuery] string? eventType = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 50)
        {
            try
            {
                var query = _context.SmartContractLogs.AsQueryable();

                if (!string.IsNullOrEmpty(eventType))
                {
                    query = query.Where(l => l.EventType == eventType);
                }

                var totalCount = await query.CountAsync();
                var logs = await query
                    .OrderByDescending(l => l.Timestamp)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .Select(l => new
                    {
                        l.Id,
                        l.EventType,
                        l.TransactionHash,
                        l.BlockNumber,
                        l.FromAddress,
                        l.ToAddress,
                        l.Amount,
                        l.Timestamp
                    })
                    .ToListAsync();

                return Ok(new
                {
                    logs,
                    totalCount,
                    page,
                    pageSize,
                    totalPages = (int)Math.Ceiling((double)totalCount / pageSize)
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get contract logs");
                return StatusCode(500, new { message = "Failed to get contract logs" });
            }
        }

        [HttpGet("contract-stats")]
        public async Task<IActionResult> GetContractStats()
        {
            try
            {
                // Calculate stats from database instead of smart contract
                var today = DateTime.UtcNow.Date;

                // Get all settled bets
                var settledBets = await _context.ActiveBets
                    .Where(b => b.IsSettled)
                    .ToListAsync();

                // Calculate total game profit (house edge)
                var totalBetAmount = settledBets.Sum(b => b.BetAmount);
                var totalPayouts = settledBets.Sum(b => b.PayoutAmount);
                var totalGameProfit = totalBetAmount - totalPayouts;

                // Calculate daily profit - use SettledAt instead of CreatedAt
                var todayBets = settledBets.Where(b => b.SettledAt.HasValue && b.SettledAt.Value.Date == today);
                var dailyBetAmount = todayBets.Sum(b => b.BetAmount);
                var dailyPayouts = todayBets.Sum(b => b.PayoutAmount);
                var currentDailyProfit = dailyBetAmount - dailyPayouts;

                // Debug logging
                _logger.LogInformation("📊 Profit Stats - Total Bets: {TotalBets}, Daily Bets: {DailyBets}, Daily Profit: {DailyProfit} PM",
                    settledBets.Count, todayBets.Count(), currentDailyProfit);

                // Get daily target
                var dailyTarget = await _context.DailyTargetTrackings
                    .Where(t => t.Date.Date == today)
                    .FirstOrDefaultAsync();

                var stats = new
                {
                    totalGameVolume = totalBetAmount.ToString("F6"),
                    totalGameProfit = totalGameProfit.ToString("F6"),
                    dailyProfitTarget = (dailyTarget?.TargetAmount ?? 750000m).ToString("F6"),
                    currentDailyProfit = currentDailyProfit.ToString("F6"),
                    treasuryBalance = "100000000.000000", // Static for now
                    isProfitTargetMet = currentDailyProfit >= (dailyTarget?.TargetAmount ?? 750000m),

                    // Additional stats
                    totalBets = settledBets.Count,
                    dailyBets = todayBets.Count(),
                    totalDeposits = await _context.SmartContractLogs
                        .Where(l => l.EventType == "TreasuryDeposit")
                        .SumAsync(l => l.Amount),

                    totalWithdrawals = await _context.SmartContractLogs
                        .Where(l => l.EventType == "TreasuryWithdrawal")
                        .SumAsync(l => l.Amount)
                };

                return Ok(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get contract stats");
                return StatusCode(500, new { message = "Failed to get contract stats" });
            }
        }

        // ===== HELPER METHODS =====

        private async Task<decimal> GetCurrentPMPrice()
        {
            var currentPrice = await _context.PMCoinPrices
                .Where(p => p.IsActive)
                .OrderByDescending(p => p.UpdatedAt)
                .FirstOrDefaultAsync();

            return currentPrice?.Price ?? 0;
        }

        private async Task<Guid?> GetCurrentSessionId()
        {
            var currentSession = await _context.CurrentGameSessions
                .Where(s => !s.IsCompleted)
                .OrderByDescending(s => s.StartTime)
                .FirstOrDefaultAsync();

            return currentSession?.Id;
        }
    }

    // ===== DTOs =====

    public class TreasuryDepositDto
    {
        [Required]
        public string TransactionHash { get; set; } = string.Empty;
        
        [Required]
        public long BlockNumber { get; set; }
        
        [Required]
        public string OwnerAddress { get; set; } = string.Empty;
        
        [Required]
        public decimal Amount { get; set; }
    }

    public class TreasuryWithdrawalDto
    {
        [Required]
        public string TransactionHash { get; set; } = string.Empty;
        
        [Required]
        public long BlockNumber { get; set; }
        
        [Required]
        public string OwnerAddress { get; set; } = string.Empty;
        
        [Required]
        public decimal Amount { get; set; }
    }

    public class GameBetLogDto
    {
        [Required]
        public string TransactionHash { get; set; } = string.Empty;
        
        [Required]
        public long BlockNumber { get; set; }
        
        [Required]
        public long BetId { get; set; }
        
        [Required]
        public string PlayerAddress { get; set; } = string.Empty;
        
        [Required]
        public decimal BetAmount { get; set; }
        
        [Required]
        public bool IsUp { get; set; }
    }

    public class BetResolutionLogDto
    {
        [Required]
        public string TransactionHash { get; set; } = string.Empty;

        [Required]
        public long BlockNumber { get; set; }

        [Required]
        public long BetId { get; set; }

        [Required]
        public string PlayerAddress { get; set; } = string.Empty;

        [Required]
        public int Result { get; set; } // 0 = LOSE, 1 = WIN, 2 = DRAW

        [Required]
        public decimal PayoutAmount { get; set; }
    }

    public class TransferPMCoinDto
    {
        [Required]
        public string FromAddress { get; set; } = string.Empty;

        [Required]
        public string ToAddress { get; set; } = string.Empty;

        [Required]
        public decimal Amount { get; set; }

        public string? Reason { get; set; }
    }

    public class SmartContractUpdateDto
    {
        [Required]
        public string Address { get; set; } = string.Empty;

        [Required]
        public string WalletBalance { get; set; } = string.Empty;

        [Required]
        public string InternalBalance { get; set; } = string.Empty;

        public object? ContractStats { get; set; }

        [Required]
        public string Timestamp { get; set; } = string.Empty;
    }

    public class ResolveBetDto
    {
        [Required]
        public Guid BetId { get; set; }

        [Required]
        public string PlayerAddress { get; set; } = string.Empty;

        [Required]
        public decimal Amount { get; set; }

        [Required]
        public int Result { get; set; } // 0 = LOSE, 1 = WIN, 2 = DRAW
    }

    public class EmergencyPayoutDto
    {
        [Required]
        public string PlayerAddress { get; set; } = string.Empty;

        [Required]
        public decimal Amount { get; set; }

        [Required]
        public string Reason { get; set; } = string.Empty;
    }

    public class MetaMaskBalanceSyncDto
    {
        [Required]
        public string Address { get; set; } = string.Empty;

        [Required]
        public string WalletBalance { get; set; } = string.Empty;

        [Required]
        public string InternalBalance { get; set; } = string.Empty;

        [Required]
        public string Timestamp { get; set; } = string.Empty;
    }

    public class EmergencyPayoutLogDto
    {
        [Required]
        public string TransactionHash { get; set; } = string.Empty;

        [Required]
        public long BlockNumber { get; set; }

        [Required]
        public string AdminAddress { get; set; } = string.Empty;

        [Required]
        public string PlayerAddress { get; set; } = string.Empty;

        [Required]
        public decimal Amount { get; set; }
    }
}
